import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>er,
  Card,
  Table,
  <PERSON>ton,
  Modal,
  Form,
  Alert,
  Spinner,
  Badge,
  Row,
  Col,
  Breadcrumb,
  InputGroup,
  Dropdown,
  OverlayTrigger,
  Tooltip,
  Tabs,
  Tab
} from 'react-bootstrap';
import {
  FaPlus,
  FaPencilAlt,
  FaTrashAlt,
  FaEye,
  FaHome,
  FaBox,
  FaSearch,
  FaFilter,
  FaTags,
  FaTag,
  FaLayerGroup,
  FaSort,
  FaSortUp,
  FaSortDown,
  FaInfoCircle
} from 'react-icons/fa';
import { fetchProducts, createProduct, updateProduct, deleteProduct, searchProducts } from '../../services/productService';
import { fetchBrands } from '../../services/brandService';
import { fetchCategories, fetchAllSousCategories, fetchAllSousSousCategories, fetchModelImages } from '../../services/categoryService';
import axios from 'axios';
import TablePagination from 'components/TablePagination';
import 'ui-component/extended/ProfessionalModal.css';

const ProductManagement = () => {
  // State management
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [brands, setBrands] = useState([]);
  const [categories, setCategories] = useState([]);
  const [sousCategories, setSousCategories] = useState([]);
  const [sousSousCategories, setSousSousCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false); // Separate loading state for form submission
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [modalAction, setModalAction] = useState('create'); // 'create' or 'edit'
  const [currentProduct, setCurrentProduct] = useState(null);
  const [productToDelete, setProductToDelete] = useState(null);
  const [productToView, setProductToView] = useState(null);
  const [activeTab, setActiveTab] = useState('basic'); // 'basic', 'images', 'attributes', 'variants'

  // Filter and search states - Restored for actual API structure
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBrand, setSelectedBrand] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedSousCategory, setSelectedSousCategory] = useState('');
  const [selectedSousSousCategory, setSelectedSousSousCategory] = useState('');
  const [stockFilter, setStockFilter] = useState(''); // 'in_stock', 'out_of_stock', ''
  const [priceRange, setPriceRange] = useState({ min: '', max: '' });
  const [sortField, setSortField] = useState('nom_produit'); // Restored to actual API field name
  const [sortDirection, setSortDirection] = useState('asc');

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Form data - Updated to match actual API response structure
  const [formData, setFormData] = useState({
    nom_produit: '',
    description_produit: '',
    prix_produit: '',
    quantite_produit: '',
    reference: '',
    marque_id: '',
    sous_sous_categorie_id: '', // API actually uses 3-level hierarchy
    // UI-only fields for category hierarchy (not sent to API)
    categorie_id: '',
    sous_categorie_id: ''
  });

  // Image states - Multiple images support
  const [images, setImages] = useState([]);
  const [primaryImageIndex, setPrimaryImageIndex] = useState(0);

  // Attributes states
  const [availableAttributes, setAvailableAttributes] = useState([]);
  const [attributeValues, setAttributeValues] = useState({});

  // Variants states
  const [variants, setVariants] = useState([]);
  const [showVariantModal, setShowVariantModal] = useState(false);
  const [newVariant, setNewVariant] = useState({
    sku: '',
    prix_supplement: 0,
    stock: 0,
    attributs: {}
  });

  // Filtered categories for modal hierarchy - Restored 3-level hierarchy
  const [filteredSousCategories, setFilteredSousCategories] = useState([]);
  const [filteredSousSousCategories, setFilteredSousSousCategories] = useState([]);

  // Product images state
  const [productImages, setProductImages] = useState({});
  const [existingImages, setExistingImages] = useState([]);
  const [updatingImageId, setUpdatingImageId] = useState(null);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Auto-hide success messages after 5 seconds
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        setSuccess('');
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  // Apply filters when dependencies change - Restored for 3-level hierarchy
  useEffect(() => {
    applyFilters();
  }, [
    products,
    searchTerm,
    selectedBrand,
    selectedCategory,
    selectedSousCategory,
    selectedSousSousCategory,
    stockFilter,
    priceRange,
    sortField,
    sortDirection
  ]);

  // Filter sous-categories when category changes in modal - Restored for 3-level hierarchy
  useEffect(() => {
    console.log('🔄 Category changed:', formData.categorie_id);
    console.log('📋 Available sous-categories:', sousCategories.length);

    if (formData.categorie_id) {
      const filtered = sousCategories.filter((sc) => sc.categorie_id === parseInt(formData.categorie_id));
      console.log('✅ Filtered sous-categories:', filtered.length);
      setFilteredSousCategories(filtered);
    } else {
      setFilteredSousCategories([]);
    }
    // Reset dependent fields when category changes
    if (formData.categorie_id) {
      setFormData((prev) => ({ ...prev, sous_categorie_id: '', sous_sous_categorie_id: '' }));
      setFilteredSousSousCategories([]);
    }
  }, [formData.categorie_id, sousCategories]);

  // Filter sous-sous-categories when sous-category changes in modal
  useEffect(() => {
    console.log('🔄 Sous-category changed:', formData.sous_categorie_id);
    console.log('📋 Available sous-sous-categories:', sousSousCategories.length);

    if (formData.sous_categorie_id) {
      const filtered = sousSousCategories.filter((ssc) => ssc.sous_categorie_id === parseInt(formData.sous_categorie_id));
      console.log('✅ Filtered sous-sous-categories:', filtered.length);
      setFilteredSousSousCategories(filtered);
    } else {
      setFilteredSousSousCategories([]);
    }
    // Reset dependent field when sous-category changes
    if (formData.sous_categorie_id) {
      setFormData((prev) => ({ ...prev, sous_sous_categorie_id: '' }));
    }
  }, [formData.sous_categorie_id, sousSousCategories]);

  // Load attributes when sous-sous-category changes
  useEffect(() => {
    if (formData.sous_sous_categorie_id) {
      loadAttributesForCategory(formData.sous_sous_categorie_id);
    } else {
      setAvailableAttributes([]);
      setAttributeValues({});
    }
  }, [formData.sous_sous_categorie_id]);

  // Function to fetch images for products
  const fetchProductImages = async (products) => {
    const imagePromises = products.map(async (product) => {
      try {
        const images = await fetchModelImages('produit', product.id);
        return { productId: product.id, images };
      } catch (error) {
        console.error(`Error fetching images for product ${product.id}:`, error);
        return { productId: product.id, images: [] };
      }
    });

    const imageResults = await Promise.all(imagePromises);
    const imageMap = {};
    imageResults.forEach(({ productId, images }) => {
      imageMap[productId] = images;
    });

    setProductImages(imageMap);
  };

  // Function to get primary image for a product
  const getPrimaryImage = (productId) => {
    const images = productImages[productId] || [];
    const primaryImage = images.find((img) => img.is_primary) || images[0];
    return primaryImage?.thumbnail_medium || primaryImage?.direct_url || null;
  };

  const loadInitialData = async () => {
    try {
      setLoading(true);
      setError('');

      console.log('🔄 Loading initial data...');
      console.log('🌐 API URLs being used:');
      console.log('- Products API:', import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api');
      console.log('- Environment:', import.meta.env.MODE);

      // Load data with individual error handling
      const results = await Promise.allSettled([
        fetchProducts(),
        fetchBrands(),
        fetchCategories(),
        fetchAllSousCategories(),
        fetchAllSousSousCategories()
      ]);

      const [productsRes, brandsRes, categoriesRes, sousCategoriesRes, sousSousCategoriesRes] = results;

      // Handle products
      if (productsRes.status === 'fulfilled') {
        const products = productsRes.value.data || productsRes.value;
        const productArray = Array.isArray(products) ? products : [];
        setProducts(productArray);
        console.log('✅ Products loaded:', productArray.length);
        if (productArray.length > 0) {
          console.log('📋 Sample product structure:', productArray[0]);
          // Fetch images for all products
          console.log('🖼️ Fetching images for products...');
          await fetchProductImages(productArray);
          console.log('✅ Product images loaded');
        }
      } else {
        console.error('❌ Failed to load products:', productsRes.reason);
        setProducts([]);
      }

      // Handle brands
      if (brandsRes.status === 'fulfilled') {
        const brands = brandsRes.value.data || brandsRes.value;
        setBrands(Array.isArray(brands) ? brands : []);
        console.log('✅ Brands loaded:', brands.length);
      } else {
        console.error('❌ Failed to load brands:', brandsRes.reason);
        setBrands([]);
      }

      // Handle categories
      if (categoriesRes.status === 'fulfilled') {
        const categories = categoriesRes.value.data || categoriesRes.value;
        setCategories(Array.isArray(categories) ? categories : []);
        console.log('✅ Categories loaded:', categories.length);
        if (categories.length > 0) {
          console.log('📋 Sample category structure:', categories[0]);
        }
      } else {
        console.error('❌ Failed to load categories:', categoriesRes.reason);
        setCategories([]);
      }

      // Handle sous-categories
      if (sousCategoriesRes.status === 'fulfilled') {
        const sousCategories = sousCategoriesRes.value.data || sousCategoriesRes.value;
        setSousCategories(Array.isArray(sousCategories) ? sousCategories : []);
        console.log('✅ Sous-categories loaded:', sousCategories.length);
        if (sousCategories.length > 0) {
          console.log('📋 Sample sous-category structure:', sousCategories[0]);
        }
      } else {
        console.error('❌ Failed to load sous-categories:', sousCategoriesRes.reason);
        setSousCategories([]);
      }

      // Handle sous-sous-categories
      if (sousSousCategoriesRes.status === 'fulfilled') {
        const sousSousCategories = sousSousCategoriesRes.value.data || sousSousCategoriesRes.value;
        setSousSousCategories(Array.isArray(sousSousCategories) ? sousSousCategories : []);
        console.log('✅ Sous-sous-categories loaded:', sousSousCategories.length);
        if (sousSousCategories.length > 0) {
          console.log('📋 Sample sous-sous-category structure:', sousSousCategories[0]);
        }
      } else {
        console.error('❌ Failed to load sous-sous-categories:', sousSousCategoriesRes.reason);
        setSousSousCategories([]);
      }

      // Check if any critical data failed to load
      const failedRequests = results.filter((result) => result.status === 'rejected');
      if (failedRequests.length > 0) {
        const errorMessages = failedRequests.map((result) => result.reason?.message || 'Erreur inconnue');
        console.warn('⚠️ Some data failed to load:', errorMessages);

        // Only show error if products failed to load (critical)
        if (productsRes.status === 'rejected') {
          setError(`Impossible de charger les produits: ${productsRes.reason?.message || 'Erreur inconnue'}`);
        } else if (failedRequests.length === results.length) {
          setError(`Toutes les données ont échoué à charger. Vérifiez votre connexion.`);
        } else {
          setError(`Certaines données n'ont pas pu être chargées: ${errorMessages.join(', ')}`);
        }
      }

      console.log('✅ Initial data loading completed');
    } catch (err) {
      console.error('❌ Critical error loading data:', err);
      setError(`Erreur critique lors du chargement des données: ${err.message}`);

      // Fallback: Set empty arrays to prevent crashes
      setProducts([]);
      setBrands([]);
      setCategories([]);
      setSousCategories([]);
      setSousSousCategories([]);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...products];

    // Search filter - Restored for actual API field names
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (product) =>
          product.nom_produit?.toLowerCase().includes(term) ||
          product.reference?.toLowerCase().includes(term) ||
          product.description_produit?.toLowerCase().includes(term)
      );
    }

    // Brand filter
    if (selectedBrand) {
      filtered = filtered.filter((product) => product.marque_id === parseInt(selectedBrand));
    }

    // Category filters - Restored for 3-level hierarchy
    if (selectedSousSousCategory) {
      filtered = filtered.filter((product) => product.sous_sous_categorie_id === parseInt(selectedSousSousCategory));
    } else if (selectedSousCategory) {
      const sousSousIds = sousSousCategories.filter((ssc) => ssc.sous_categorie_id === parseInt(selectedSousCategory)).map((ssc) => ssc.id);
      filtered = filtered.filter((product) => sousSousIds.includes(product.sous_sous_categorie_id));
    } else if (selectedCategory) {
      const sousIds = sousCategories.filter((sc) => sc.categorie_id === parseInt(selectedCategory)).map((sc) => sc.id);
      const sousSousIds = sousSousCategories.filter((ssc) => sousIds.includes(ssc.sous_categorie_id)).map((ssc) => ssc.id);
      filtered = filtered.filter((product) => sousSousIds.includes(product.sous_sous_categorie_id));
    }

    // Stock filter - Restored for actual API field names
    if (stockFilter === 'in_stock') {
      filtered = filtered.filter((product) => product.quantite_produit > 0);
    } else if (stockFilter === 'out_of_stock') {
      filtered = filtered.filter((product) => product.quantite_produit <= 0);
    }

    // Price range filter - Restored for actual API field names
    if (priceRange.min !== '') {
      filtered = filtered.filter((product) => parseFloat(product.prix_produit) >= parseFloat(priceRange.min));
    }
    if (priceRange.max !== '') {
      filtered = filtered.filter((product) => parseFloat(product.prix_produit) <= parseFloat(priceRange.max));
    }

    // Sorting
    filtered.sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      // Handle numeric fields - Restored for actual API field names
      if (sortField === 'prix_produit' || sortField === 'quantite_produit') {
        aValue = parseFloat(aValue) || 0;
        bValue = parseFloat(bValue) || 0;
      } else {
        aValue = aValue?.toString().toLowerCase() || '';
        bValue = bValue?.toString().toLowerCase() || '';
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredProducts(filtered);
    setCurrentPage(1);
  };

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (field) => {
    if (sortField !== field) return <FaSort className="text-muted" />;
    return sortDirection === 'asc' ? <FaSortUp className="text-primary" /> : <FaSortDown className="text-primary" />;
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Multiple images handling
  const handleImagesChange = (e) => {
    const files = Array.from(e.target.files);
    const newImages = files.filter((f) => !images.some((img) => img.name === f.name));
    setImages((prev) => [...prev, ...newImages]);

    // If no primary selected, set the first as primary
    if (images.length === 0 && newImages.length > 0) {
      setPrimaryImageIndex(0);
    }
  };

  const removeImage = (index) => {
    setImages((prev) => prev.filter((_, i) => i !== index));

    // Adjust primary index if needed
    if (index === primaryImageIndex) {
      setPrimaryImageIndex(0);
    } else if (index < primaryImageIndex) {
      setPrimaryImageIndex((prev) => prev - 1);
    }
  };

  const setPrimaryImage = (index) => {
    setPrimaryImageIndex(index);
  };

  // Enhanced attributes handling with new system
  const loadAttributesForCategory = async (sousSousCategorieId) => {
    if (!sousSousCategorieId) {
      setAvailableAttributes([]);
      return;
    }

    try {
      // Use the new attributes API endpoint
      const response = await fetch(`${API_URL}/admin/attributs?sous_categorie_id=${sousSousCategorieId}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        const attributes = data.data || data;
        console.log('✅ Loaded attributes for category:', attributes.length);
        setAvailableAttributes(attributes);
      } else {
        console.warn('⚠️ Failed to load attributes, using fallback');
        // Fallback to mock attributes
        const mockAttributes = [
          { id: 1, nom: 'Couleur', type_valeur: 'texte', obligatoire: true, groupe: { nom: 'Apparence' } },
          { id: 2, nom: 'Taille', type_valeur: 'texte', obligatoire: false, groupe: { nom: 'Dimensions' } },
          { id: 3, nom: 'Matériau', type_valeur: 'texte', obligatoire: false, groupe: { nom: 'Composition' } },
          { id: 4, nom: 'Poids', type_valeur: 'nombre', obligatoire: false, groupe: { nom: 'Dimensions' } }
        ];
        setAvailableAttributes(mockAttributes);
      }
    } catch (error) {
      console.error('Error loading attributes:', error);
      // Fallback to mock attributes
      const mockAttributes = [
        { id: 1, nom: 'Couleur', type_valeur: 'texte', obligatoire: true, groupe: { nom: 'Apparence' } },
        { id: 2, nom: 'Taille', type_valeur: 'texte', obligatoire: false, groupe: { nom: 'Dimensions' } },
        { id: 3, nom: 'Matériau', type_valeur: 'texte', obligatoire: false, groupe: { nom: 'Composition' } },
        { id: 4, nom: 'Poids', type_valeur: 'nombre', obligatoire: false, groupe: { nom: 'Dimensions' } }
      ];
      setAvailableAttributes(mockAttributes);
    }
  };

  const handleAttributeChange = (attributId, value) => {
    setAttributeValues((prev) => ({
      ...prev,
      [attributId]: value
    }));
  };

  // Enhanced attribute input renderer for different types
  const renderAttributeInput = (attribute) => {
    const value = attributeValues[attribute.id] || '';
    const isRequired = attribute.obligatoire && formData.sous_sous_categorie_id;

    switch (attribute.type_valeur) {
      case 'nombre':
        return (
          <Form.Control
            name={`attribute_${attribute.id}`}
            type="number"
            step="0.01"
            value={value}
            onChange={(e) => handleAttributeChange(attribute.id, e.target.value)}
            required={isRequired}
            placeholder={`Entrez ${attribute.nom.toLowerCase()}`}
          />
        );

      case 'date':
        return (
          <Form.Control
            name={`attribute_${attribute.id}`}
            type="date"
            value={value}
            onChange={(e) => handleAttributeChange(attribute.id, e.target.value)}
            required={isRequired}
          />
        );

      case 'booleen':
        return (
          <Form.Check
            name={`attribute_${attribute.id}`}
            type="checkbox"
            checked={value === 'true' || value === true}
            onChange={(e) => handleAttributeChange(attribute.id, e.target.checked)}
            label={`Activer ${attribute.nom.toLowerCase()}`}
            required={isRequired}
          />
        );

      case 'liste':
        // For list type, you might want to fetch available options from API
        return (
          <Form.Select
            name={`attribute_${attribute.id}`}
            value={value}
            onChange={(e) => handleAttributeChange(attribute.id, e.target.value)}
            required={isRequired}
          >
            <option value="">Sélectionnez {attribute.nom.toLowerCase()}</option>
            {/* Add options here - could be fetched from API */}
            <option value="option1">Option 1</option>
            <option value="option2">Option 2</option>
          </Form.Select>
        );

      case 'texte':
      default:
        return (
          <Form.Control
            name={`attribute_${attribute.id}`}
            type="text"
            value={value}
            onChange={(e) => handleAttributeChange(attribute.id, e.target.value)}
            required={isRequired}
            placeholder={`Entrez ${attribute.nom.toLowerCase()}`}
          />
        );
    }
  };

  // Variants handling
  const addVariant = () => {
    if (!newVariant.sku) {
      setError('Le SKU est obligatoire pour la variante');
      return;
    }

    setVariants((prev) => [...prev, { ...newVariant, id: Date.now() }]);
    setNewVariant({
      sku: '',
      prix_supplement: 0,
      stock: 0,
      attributs: {}
    });
    setShowVariantModal(false);
  };

  const removeVariant = (index) => {
    setVariants((prev) => prev.filter((_, i) => i !== index));
  };

  // Function to set an existing image as primary
  const setExistingImageAsPrimary = async (imageId, productId) => {
    try {
      setUpdatingImageId(imageId);
      console.log(`🔄 Setting image ${imageId} as primary for product ${productId}`);

      // Try the alternative endpoint first (like in EditProduit.jsx)
      const token = localStorage.getItem('access_token');
      const headers = {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` })
      };

      console.log('🔑 Using auth token:', !!token);
      console.log('📋 Headers:', headers);

      // NOUVELLE APPROCHE: Gérer la logique métier côté client
      console.log('📋 Current existing images:', existingImages);

      // Étape 1: Désactiver l'image principale actuelle
      const currentPrimaryImage = existingImages.find((img) => img.is_primary);
      if (currentPrimaryImage && currentPrimaryImage.id !== imageId) {
        console.log(`🔄 Removing primary status from image ${currentPrimaryImage.id}`);
        try {
          await axios.put(`https://laravel-api.fly.dev/api/images/${currentPrimaryImage.id}`, { is_primary: false }, { headers });
          console.log('✅ Removed primary status from current image');
        } catch (removePrimaryError) {
          console.log('⚠️ Failed to remove primary status:', removePrimaryError.response?.status);
          // Continue anyway, the server might handle this automatically
        }
      }

      // Étape 2: Définir la nouvelle image comme principale
      try {
        // Try the set-primary endpoint first
        await axios.patch(
          `https://laravel-api.fly.dev/api/images/set-primary`,
          {
            model_type: 'produit',
            model_id: productId,
            image_id: imageId
          },
          { headers }
        );
        console.log('✅ Used set-primary endpoint successfully');
      } catch (setPrimaryError) {
        console.log('⚠️ set-primary failed, trying direct PUT:', setPrimaryError.response?.status);
        console.log('⚠️ set-primary error details:', setPrimaryError.response?.data);

        // Fallback to direct PUT method
        try {
          await axios.put(`https://laravel-api.fly.dev/api/images/${imageId}`, { is_primary: true }, { headers });
          console.log('✅ Used direct PUT endpoint successfully');
        } catch (directPutError) {
          console.log('⚠️ Direct PUT also failed, trying reorder endpoint:', directPutError.response?.status);
          console.log('⚠️ Direct PUT error details:', directPutError.response?.data);

          // Last resort: Use reorder endpoint to set primary
          const imageOrder = existingImages.map((img) => ({
            id: img.id,
            order: img.id === imageId ? 0 : img.order || 1,
            is_primary: img.id === imageId
          }));

          await axios.post(
            `https://laravel-api.fly.dev/api/images/reorder`,
            {
              model_type: 'produit',
              model_id: productId,
              images: imageOrder
            },
            { headers }
          );
          console.log('✅ Used reorder endpoint successfully');
        }
      }

      console.log('✅ Image set as primary successfully');
      setSuccess('Image principale mise à jour avec succès');

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000);

      // Reload existing images to reflect the change
      if (modalAction === 'edit' && currentProduct) {
        const updatedImages = await fetchModelImages('produit', currentProduct.id);
        setExistingImages(updatedImages);
      }

      // Reload product images for the table and details modal
      const productToUpdate = currentProduct || productToView;
      if (productToUpdate) {
        await fetchProductImages([productToUpdate]);
      }
    } catch (error) {
      console.error('❌ Error setting image as primary:', error);
      console.error('❌ Error response:', error.response);
      console.error('❌ Error data:', error.response?.data);
      console.error('❌ Error status:', error.response?.status);

      const errorMessage =
        error.response?.data?.message ||
        error.response?.data?.error ||
        error.message ||
        "Erreur lors de la mise à jour de l'image principale";
      setError(`Erreur ${error.response?.status || 'API'}: ${errorMessage}`);

      // Clear error message after 5 seconds
      setTimeout(() => setError(''), 5000);
    } finally {
      setUpdatingImageId(null);
    }
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedBrand('');
    setSelectedCategory('');
    setSelectedSousCategory('');
    setSelectedSousSousCategory('');
    setStockFilter('');
    setPriceRange({ min: '', max: '' });
    setSortField('nom_produit'); // Restored for actual API field name
    setSortDirection('asc');
  };

  const handleCreate = () => {
    setModalAction('create');
    setCurrentProduct(null);
    setFormData({
      nom_produit: '',
      description_produit: '',
      prix_produit: '',
      quantite_produit: '',
      reference: '',
      marque_id: '',
      sous_sous_categorie_id: '', // Restored for actual API structure
      // UI-only fields for category hierarchy
      categorie_id: '',
      sous_categorie_id: ''
    });

    // Reset all states
    setImages([]);
    setExistingImages([]);
    setPrimaryImageIndex(0);
    setAvailableAttributes([]);
    setAttributeValues({});
    setVariants([]);
    setFilteredSousCategories([]);
    setFilteredSousSousCategories([]);
    setActiveTab('basic');
    setShowModal(true);
  };

  const handleEdit = (product) => {
    setModalAction('edit');
    setCurrentProduct(product);

    // Find the category hierarchy for this product - Restored for 3-level hierarchy
    const sousSousCategorie = sousSousCategories.find((ssc) => ssc.id === product.sous_sous_categorie_id);
    const sousCategorie = sousSousCategorie ? sousCategories.find((sc) => sc.id === sousSousCategorie.sous_categorie_id) : null;
    const categorie = sousCategorie ? categories.find((c) => c.id === sousCategorie.categorie_id) : null;

    setFormData({
      nom_produit: product.nom_produit || '',
      description_produit: product.description_produit || '',
      prix_produit: product.prix_produit || '',
      quantite_produit: product.quantite_produit || '',
      reference: product.reference || '',
      marque_id: product.marque_id || '',
      sous_sous_categorie_id: product.sous_sous_categorie_id || '',
      // UI-only fields for category hierarchy
      categorie_id: categorie?.id || '',
      sous_categorie_id: sousCategorie?.id || ''
    });

    // Set filtered options based on current selection
    if (categorie) {
      const filteredSous = sousCategories.filter((sc) => sc.categorie_id === categorie.id);
      setFilteredSousCategories(filteredSous);
    }
    if (sousCategorie) {
      const filteredSousSous = sousSousCategories.filter((ssc) => ssc.sous_categorie_id === sousCategorie.id);
      setFilteredSousSousCategories(filteredSousSous);
    }

    // Load existing images for edit
    const loadExistingImages = async () => {
      try {
        const existingImages = await fetchModelImages('produit', product.id);
        setExistingImages(existingImages);
        console.log('📸 Loaded existing images for product:', existingImages.length);
      } catch (error) {
        console.error('Error loading existing images:', error);
        setExistingImages([]);
      }
    };

    setImages([]); // Reset new images for edit
    setPrimaryImageIndex(0);
    setActiveTab('basic');
    loadExistingImages();
    setShowModal(true);
  };

  const handleDelete = (product) => {
    setProductToDelete(product);
    setShowDeleteModal(true);
  };

  const handleViewDetails = (product) => {
    setProductToView(product);
    setShowDetailsModal(true);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    console.log('🚀 Form submission started');
    console.log('📋 Form data:', formData);
    console.log('🖼️ Images:', images.length);

    try {
      setSubmitting(true); // Use separate submitting state
      setError('');
      setSuccess(''); // Clear previous success messages

      // Validate required fields - Restored for actual API field names
      if (
        !formData.nom_produit ||
        !formData.prix_produit ||
        !formData.quantite_produit ||
        !formData.marque_id ||
        !formData.sous_sous_categorie_id
      ) {
        throw new Error('Veuillez remplir tous les champs obligatoires');
      }

      // Prepare API data - Restored to match actual API structure
      const apiData = {
        nom_produit: formData.nom_produit,
        description_produit: formData.description_produit,
        prix_produit: formData.prix_produit,
        quantite_produit: formData.quantite_produit,
        reference: formData.reference,
        marque_id: formData.marque_id,
        sous_sous_categorie_id: formData.sous_sous_categorie_id
      };

      // Add attributes if any - Updated to match documentation structure
      if (Object.keys(attributeValues).length > 0) {
        apiData.attributs = Object.entries(attributeValues).map(([attribut_id, valeur]) => ({
          attribut_id: parseInt(attribut_id),
          valeur
        }));
      }

      // Add variants if any
      if (variants.length > 0) {
        apiData.variants = variants;
      }

      console.log('📝 API Data being sent:', apiData);
      console.log('🖼️ Images being sent:', images.length);
      console.log('🏷️ Attributes being sent:', attributeValues);
      console.log('🔄 Variants being sent:', variants);

      if (modalAction === 'create') {
        console.log('🚀 Starting product creation...');
        console.log('📊 Has images:', images.length > 0);
        console.log('📋 JSON data:', JSON.stringify(apiData, null, 2));

        // Step 1: Create the product (always JSON)
        console.log('📤 Creating product...');
        const result = await createProduct(apiData);
        console.log('✅ Product created via service:', result);

        // Get the created product ID
        const createdProduct = result?.data || result;
        const productId = createdProduct?.id;

        if (!productId) {
          console.error('❌ No product ID in response:', result);
          throw new Error('Produit créé mais ID non récupéré');
        }

        console.log('📋 Created product ID:', productId);

        // Step 2: Upload images if any (separate API call)
        if (images.length > 0) {
          console.log('🖼️ Uploading images for product:', productId);

          try {
            const imagesFormData = new FormData();
            images.forEach((img) => {
              imagesFormData.append('images[]', img);
            });
            imagesFormData.append('model_type', 'produit');
            imagesFormData.append('model_id', productId);
            imagesFormData.append('primary_index', primaryImageIndex);

            // Get authentication token for image upload
            const token = localStorage.getItem('access_token');
            const imageHeaders = {
              Accept: 'application/json'
            };

            if (token) {
              imageHeaders['Authorization'] = `Bearer ${token}`;
            }

            console.log('📤 Uploading images...');
            const imageResponse = await fetch(
              `${import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api'}/images/upload-multiple`,
              {
                method: 'POST',
                headers: imageHeaders,
                body: imagesFormData
              }
            );

            if (!imageResponse.ok) {
              const errorImg = await imageResponse.text();
              console.warn('⚠️ Image upload failed:', errorImg);
              // Don't throw error, product was created successfully
              setSuccess('Produit créé avec succès (images non uploadées)');
            } else {
              console.log('✅ Images uploaded successfully');
              setSuccess('Produit créé avec succès');
            }
          } catch (imgErr) {
            console.warn('⚠️ Image upload error:', imgErr);
            // Don't throw error, product was created successfully
            setSuccess('Produit créé avec succès (erreur upload images)');
          }
        } else {
          setSuccess('Produit créé avec succès');
        }

        console.log('✅ Product creation process completed');
      } else {
        // Update logic: always use JSON for product update, handle images separately
        console.log('🔄 Starting product update...');
        console.log('📊 Has images:', images.length > 0);
        console.log('📋 JSON data:', JSON.stringify(apiData, null, 2));

        // Step 1: Update the product (always JSON)
        await updateProduct(currentProduct.id, apiData);
        console.log('✅ Product updated via service');

        // Step 2: Upload new images if any (separate API call)
        if (images.length > 0) {
          console.log('🖼️ Uploading new images for product:', currentProduct.id);

          try {
            const imagesFormData = new FormData();
            images.forEach((img) => {
              imagesFormData.append('images[]', img);
            });
            imagesFormData.append('model_type', 'produit');
            imagesFormData.append('model_id', currentProduct.id);
            imagesFormData.append('primary_index', primaryImageIndex);

            // Get authentication token for image upload
            const token = localStorage.getItem('access_token');
            const imageHeaders = {
              Accept: 'application/json'
            };

            if (token) {
              imageHeaders['Authorization'] = `Bearer ${token}`;
            }

            const imageResponse = await fetch(
              `${import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api'}/images/upload-multiple`,
              {
                method: 'POST',
                headers: imageHeaders,
                body: imagesFormData
              }
            );

            if (!imageResponse.ok) {
              const errorImg = await imageResponse.json();
              console.warn('⚠️ Image upload failed:', errorImg);
              // Don't throw error, product was updated successfully
              setSuccess('Produit mis à jour avec succès (images non uploadées)');
            } else {
              console.log('✅ Images uploaded successfully');
              setSuccess('Produit mis à jour avec succès');
            }
          } catch (imgErr) {
            console.warn('⚠️ Image upload error:', imgErr);
            // Don't throw error, product was updated successfully
            setSuccess('Produit mis à jour avec succès (erreur upload images)');
          }
        } else {
          setSuccess('Produit mis à jour avec succès');
        }

        // For update: optimized local state update instead of full reload
        console.log('🔄 Updating local product state after update...');
        try {
          // Update the specific product in local state instead of full reload
          // Use the API data for local state update
          const updatedProducts = products.map((p) => (p.id === currentProduct.id ? { ...p, ...apiData, id: currentProduct.id } : p));
          setProducts(updatedProducts);
          setFilteredProducts(updatedProducts);
          console.log('✅ Local product state updated for product ID:', currentProduct.id);
        } catch (updateError) {
          console.warn('⚠️ Failed to update local state, falling back to reload:', updateError);
          // Fallback: reload products list if local update fails
          try {
            const productsResponse = await fetchProducts();
            const newProducts = productsResponse.data || productsResponse;
            const productArray = Array.isArray(newProducts) ? newProducts : [];
            setProducts(productArray);
            setFilteredProducts(productArray);
            console.log('✅ Products list reloaded as fallback (without images):', productArray.length);

            // Note: We don't fetch images here to avoid freezing the UI
            // Images will be loaded on-demand when needed
          } catch (reloadError) {
            console.warn('⚠️ Failed to reload products list:', reloadError);
            // Don't show error, the product was updated successfully
          }
        }
      }

      // Close modal and reset form
      console.log('🔄 Closing modal and resetting form...');
      setShowModal(false);
      setImages([]);
      setExistingImages([]);
      setPrimaryImageIndex(0);
      setActiveTab('basic');

      // Reset form data - Restored for actual API field names
      setFormData({
        nom_produit: '',
        description_produit: '',
        prix_produit: '',
        quantite_produit: '',
        reference: '',
        marque_id: '',
        sous_sous_categorie_id: '',
        categorie_id: '',
        sous_categorie_id: ''
      });

      // Reset other form states
      setAttributeValues({});
      setVariants([]);
      setAvailableAttributes([]);
      setFilteredSousCategories([]);
      setFilteredSousSousCategories([]);

      console.log('✅ Form reset completed');
    } catch (err) {
      console.error('❌ Error saving product:', err);
      console.error('❌ Error name:', err.name);
      console.error('❌ Error message:', err.message);
      console.error('❌ Error stack:', err.stack);

      // More detailed error handling
      let errorMessage = 'Erreur lors de la sauvegarde';

      if (err.name === 'TypeError' && err.message.includes('fetch')) {
        errorMessage = 'Impossible de se connecter au serveur. Vérifiez votre connexion internet.';
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
    } finally {
      setSubmitting(false); // Reset submitting state
    }
  };

  const confirmDelete = async () => {
    try {
      setLoading(true);
      setError(''); // Clear any previous errors

      await deleteProduct(productToDelete.id);

      // Remove the product from the local state immediately
      const updatedProducts = products.filter((p) => p.id !== productToDelete.id);
      setProducts(updatedProducts);
      setFilteredProducts(updatedProducts);

      setSuccess('Produit supprimé avec succès');
      setShowDeleteModal(false);

      // Optionally reload data to ensure consistency
      // await loadInitialData();
    } catch (err) {
      console.error('Error deleting product:', err);

      // Handle specific error cases
      if (err.message.includes("n'existe pas") || err.message.includes('déjà été supprimé')) {
        // Product was already deleted, remove it from local state
        const updatedProducts = products.filter((p) => p.id !== productToDelete.id);
        setProducts(updatedProducts);
        setFilteredProducts(updatedProducts);
        setSuccess('Produit supprimé (était déjà supprimé du serveur)');
        setShowDeleteModal(false);
      } else {
        setError(err.message || 'Erreur lors de la suppression');
      }
    } finally {
      setLoading(false);
    }
  };

  const getBrandName = (marqueId) => {
    const brand = brands.find((b) => b.id === marqueId);
    return brand ? brand.nom_marque : 'N/A';
  };

  const getCategoryPath = (sousSousCategorieId) => {
    if (!sousSousCategorieId) return 'N/A';

    const sousSousCategorie = sousSousCategories.find((ssc) => ssc.id === sousSousCategorieId);
    if (!sousSousCategorie) return 'N/A';

    // Handle both possible field names for sous-sous-categories
    const sousSousCatName = sousSousCategorie.nom || sousSousCategorie.nom_sous_sous_categorie;

    const sousCategorie = sousCategories.find((sc) => sc.id === sousSousCategorie.sous_categorie_id);
    if (!sousCategorie) return sousSousCatName || 'N/A';

    // Handle both possible field names for sous-categories
    const sousCatName = sousCategorie.nom || sousCategorie.nom_sous_categorie;

    const categorie = categories.find((c) => c.id === sousCategorie.categorie_id);
    if (!categorie) return `${sousCatName} > ${sousSousCatName}`;

    // Handle both possible field names for categories
    const catName = categorie.nom || categorie.nom_categorie;

    return `${catName} > ${sousCatName} > ${sousSousCatName}`;
  };

  // Pagination
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentProducts = filteredProducts.slice(startIndex, endIndex);

  // Show loading spinner while initial data is loading
  if (loading) {
    return (
      <Container fluid className="py-4">
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '60vh' }}>
          <div className="text-center">
            <Spinner animation="border" variant="primary" style={{ width: '4rem', height: '4rem' }} />
            <div className="mt-3 fw-medium text-primary">Chargement des produits...</div>
            <div className="text-muted small">Veuillez patienter</div>
          </div>
        </div>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-4">
        <Breadcrumb.Item href="#" onClick={() => window.history.back()}>
          <FaHome className="me-1" />
          Accueil
        </Breadcrumb.Item>
        <Breadcrumb.Item active>
          <FaBox className="me-1" />
          Gestion des Produits
        </Breadcrumb.Item>
      </Breadcrumb>

      {/* Alerts */}
      {error && (
        <Alert variant="danger" className="mb-4">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <strong>Erreur:</strong> {error}
            </div>
            <div>
              <Button variant="outline-danger" size="sm" onClick={() => window.location.reload()} className="me-2">
                Réessayer
              </Button>
              <Button variant="outline-secondary" size="sm" onClick={() => setError('')}>
                Fermer
              </Button>
            </div>
          </div>
        </Alert>
      )}
      {success && (
        <Alert variant="success" onClose={() => setSuccess('')} dismissible className="mb-4">
          {success}
        </Alert>
      )}

      {/* Header */}
      <Card className="mb-4 border-0 shadow-sm">
        <Card.Body>
          <Row className="align-items-center">
            <Col>
              <h2 className="mb-0 fw-bold text-primary">
                <FaBox className="me-2" />
                Gestion des Produits
              </h2>
              <p className="text-muted mb-0">Gérez tous vos produits en un seul endroit</p>
            </Col>
            <Col xs="auto">
              <Button variant="primary" onClick={handleCreate} className="d-flex align-items-center" disabled={submitting}>
                <FaPlus className="me-2" />
                Ajouter un Produit
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Filters */}
      <Card className="mb-4 border-0 shadow-sm">
        <Card.Header className="bg-light border-0">
          <h5 className="mb-0 d-flex align-items-center">
            <FaFilter className="me-2" />
            Filtres et Recherche
          </h5>
        </Card.Header>
        <Card.Body>
          <Row className="g-3">
            {/* Search */}
            <Col md={4}>
              <Form.Label className="fw-medium">Recherche</Form.Label>
              <InputGroup>
                <InputGroup.Text>
                  <FaSearch />
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Nom, référence, description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </Col>

            {/* Brand Filter */}
            <Col md={2}>
              <Form.Label className="fw-medium">Marque</Form.Label>
              <Form.Select value={selectedBrand} onChange={(e) => setSelectedBrand(e.target.value)}>
                <option value="">Toutes les marques</option>
                {brands.map((brand) => (
                  <option key={brand.id} value={brand.id}>
                    {brand.nom_marque}
                  </option>
                ))}
              </Form.Select>
            </Col>

            {/* Category Filter */}
            <Col md={2}>
              <Form.Label className="fw-medium">Catégorie</Form.Label>
              <Form.Select
                value={selectedCategory}
                onChange={(e) => {
                  setSelectedCategory(e.target.value);
                  setSelectedSousCategory('');
                  setSelectedSousSousCategory('');
                }}
              >
                <option value="">Toutes les catégories</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.nom || category.nom_categorie}
                  </option>
                ))}
              </Form.Select>
            </Col>

            {/* Sous-Category Filter */}
            <Col md={2}>
              <Form.Label className="fw-medium">Sous-catégorie</Form.Label>
              <Form.Select
                value={selectedSousCategory}
                onChange={(e) => {
                  setSelectedSousCategory(e.target.value);
                  setSelectedSousSousCategory('');
                }}
                disabled={!selectedCategory}
              >
                <option value="">Toutes</option>
                {sousCategories
                  .filter((sc) => sc.categorie_id === parseInt(selectedCategory))
                  .map((sousCategory) => (
                    <option key={sousCategory.id} value={sousCategory.id}>
                      {sousCategory.nom || sousCategory.nom_sous_categorie}
                    </option>
                  ))}
              </Form.Select>
            </Col>

            {/* Sous-Sous-Category Filter */}
            <Col md={2}>
              <Form.Label className="fw-medium">Sous-sous-catégorie</Form.Label>
              <Form.Select
                value={selectedSousSousCategory}
                onChange={(e) => setSelectedSousSousCategory(e.target.value)}
                disabled={!selectedSousCategory}
              >
                <option value="">Toutes</option>
                {sousSousCategories
                  .filter((ssc) => ssc.sous_categorie_id === parseInt(selectedSousCategory))
                  .map((sousSousCategory) => (
                    <option key={sousSousCategory.id} value={sousSousCategory.id}>
                      {sousSousCategory.nom || sousSousCategory.nom_sous_sous_categorie}
                    </option>
                  ))}
              </Form.Select>
            </Col>
          </Row>

          <Row className="g-3 mt-2">
            {/* Stock Filter */}
            <Col md={2}>
              <Form.Label className="fw-medium">Stock</Form.Label>
              <Form.Select value={stockFilter} onChange={(e) => setStockFilter(e.target.value)}>
                <option value="">Tous</option>
                <option value="in_stock">En stock</option>
                <option value="out_of_stock">Rupture</option>
              </Form.Select>
            </Col>

            {/* Price Range */}
            <Col md={3}>
              <Form.Label className="fw-medium">Prix (DT)</Form.Label>
              <Row>
                <Col>
                  <Form.Control
                    type="number"
                    placeholder="Min"
                    value={priceRange.min}
                    onChange={(e) => setPriceRange({ ...priceRange, min: e.target.value })}
                  />
                </Col>
                <Col xs="auto" className="d-flex align-items-center">
                  <span>-</span>
                </Col>
                <Col>
                  <Form.Control
                    type="number"
                    placeholder="Max"
                    value={priceRange.max}
                    onChange={(e) => setPriceRange({ ...priceRange, max: e.target.value })}
                  />
                </Col>
              </Row>
            </Col>

            {/* Clear Filters */}
            <Col md={2} className="d-flex align-items-end">
              <Button variant="outline-secondary" onClick={clearFilters} className="w-100">
                Effacer les filtres
              </Button>
            </Col>

            {/* Results Count */}
            <Col md={5} className="d-flex align-items-end">
              <div className="text-muted">
                <strong>{filteredProducts.length}</strong> produit(s) trouvé(s) sur <strong>{products.length}</strong>
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Products Table */}
      <Card className="border-0 shadow-sm">
        <Card.Body className="p-0">
          <div className="table-responsive">
            <Table hover className="mb-0">
              <thead className="bg-light">
                <tr>
                  <th className="border-0 px-4 py-3">Image</th>
                  <th className="border-0 px-4 py-3">
                    <Button variant="link" className="p-0 text-decoration-none fw-bold" onClick={() => handleSort('nom_produit')}>
                      Produit {getSortIcon('nom_produit')}
                    </Button>
                  </th>
                  <th className="border-0 px-4 py-3">
                    <Button variant="link" className="p-0 text-decoration-none fw-bold" onClick={() => handleSort('reference')}>
                      Référence {getSortIcon('reference')}
                    </Button>
                  </th>
                  <th className="border-0 px-4 py-3">Marque</th>
                  <th className="border-0 px-4 py-3">Catégorie</th>
                  <th className="border-0 px-4 py-3">
                    <Button variant="link" className="p-0 text-decoration-none fw-bold" onClick={() => handleSort('prix_produit')}>
                      Prix {getSortIcon('prix_produit')}
                    </Button>
                  </th>
                  <th className="border-0 px-4 py-3">
                    <Button variant="link" className="p-0 text-decoration-none fw-bold" onClick={() => handleSort('quantite_produit')}>
                      Stock {getSortIcon('quantite_produit')}
                    </Button>
                  </th>
                  <th className="border-0 px-4 py-3 text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                {currentProducts.map((product) => (
                  <tr key={product.id}>
                    <td className="px-4 py-3">
                      <div style={{ width: '60px', height: '60px' }} className="d-flex align-items-center justify-content-center">
                        {getPrimaryImage(product.id) ? (
                          <img
                            src={getPrimaryImage(product.id)}
                            alt={product.nom_produit}
                            style={{ width: '60px', height: '60px', objectFit: 'cover' }}
                            className="rounded border"
                          />
                        ) : (
                          <div
                            className="bg-light rounded border d-flex align-items-center justify-content-center"
                            style={{ width: '60px', height: '60px' }}
                          >
                            <FaBox className="text-muted" size={20} />
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      <div>
                        <div className="fw-medium">{product.nom_produit}</div>
                        {product.description_produit && (
                          <small className="text-muted">{product.description_produit.substring(0, 50)}...</small>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      <code className="bg-light px-2 py-1 rounded">{product.reference || 'N/A'}</code>
                    </td>
                    <td className="px-4 py-3">{getBrandName(product.marque_id)}</td>
                    <td className="px-4 py-3">
                      <small className="text-muted">{getCategoryPath(product.sous_sous_categorie_id)}</small>
                    </td>
                    <td className="px-4 py-3">
                      <span className="fw-medium">{product.prix_produit ? `${product.prix_produit} DT` : 'N/A'}</span>
                    </td>
                    <td className="px-4 py-3">
                      <Badge bg={product.quantite_produit > 0 ? 'success' : 'danger'} className="rounded-pill">
                        {product.quantite_produit > 0 ? `${product.quantite_produit} en stock` : 'Rupture'}
                      </Badge>
                    </td>
                    <td className="px-4 py-3 text-center">
                      <div className="d-flex justify-content-center gap-1">
                        <OverlayTrigger placement="top" overlay={<Tooltip>Voir détails</Tooltip>}>
                          <Button variant="outline-info" size="sm" onClick={() => handleViewDetails(product)}>
                            <FaEye />
                          </Button>
                        </OverlayTrigger>
                        <OverlayTrigger placement="top" overlay={<Tooltip>Modifier</Tooltip>}>
                          <Button variant="outline-primary" size="sm" onClick={() => handleEdit(product)}>
                            <FaPencilAlt />
                          </Button>
                        </OverlayTrigger>
                        <OverlayTrigger placement="top" overlay={<Tooltip>Supprimer</Tooltip>}>
                          <Button variant="outline-danger" size="sm" onClick={() => handleDelete(product)}>
                            <FaTrashAlt />
                          </Button>
                        </OverlayTrigger>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </div>

          {filteredProducts.length === 0 && (
            <div className="text-center py-5">
              <FaBox size={48} className="text-muted mb-3" />
              <h5 className="text-muted">Aucun produit trouvé</h5>
              <p className="text-muted">Essayez de modifier vos critères de recherche ou ajoutez un nouveau produit.</p>
            </div>
          )}
        </Card.Body>

        {/* Pagination */}
        {filteredProducts.length > 0 && (
          <Card.Footer className="bg-light border-0">
            <TablePagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              onItemsPerPageChange={handleItemsPerPageChange}
              totalItems={filteredProducts.length}
              itemsPerPage={itemsPerPage}
              startIndex={startIndex}
              endIndex={Math.min(endIndex, filteredProducts.length)}
              itemsPerPageOptions={[5, 10, 15, 25, 50]}
              showDirectPageInput={totalPages > 5}
              showPageInfo={true}
              showItemsPerPage={true}
            />
          </Card.Footer>
        )}
      </Card>

      {/* Create/Edit Product Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg" className="professional-modal">
        <Modal.Header closeButton className="border-0 pb-0">
          <Modal.Title className="fw-bold">
            {modalAction === 'create' ? (
              <>
                <FaPlus className="me-2 text-primary" />
                Ajouter un Produit
              </>
            ) : (
              <>
                <FaPencilAlt className="me-2 text-primary" />
                Modifier le Produit
              </>
            )}
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleSubmit}>
          <Modal.Body className="px-4" style={{ position: 'relative' }}>
            {/* Loading Overlay */}
            {submitting && (
              <div
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 1000,
                  borderRadius: '0.375rem'
                }}
              >
                <div className="text-center">
                  <Spinner animation="border" variant="primary" style={{ width: '3rem', height: '3rem' }} />
                  <div className="mt-3 fw-medium text-primary">
                    {modalAction === 'create' ? 'Création du produit en cours...' : 'Modification du produit en cours...'}
                  </div>
                  <div className="text-muted small">Veuillez patienter</div>
                </div>
              </div>
            )}

            <Tabs activeKey={activeTab} onSelect={(k) => setActiveTab(k)} className="mb-3">
              {/* Basic Information Tab */}
              <Tab eventKey="basic" title="Informations de base">
                <Row className="g-3">
                  {/* Product Name */}
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="fw-medium">
                        Nom du produit <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Control
                        name="nom_produit"
                        type="text"
                        value={formData.nom_produit}
                        onChange={(e) => setFormData({ ...formData, nom_produit: e.target.value })}
                        required
                        placeholder="Entrez le nom du produit"
                      />
                    </Form.Group>
                  </Col>

                  {/* Product Reference */}
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="fw-medium">Référence produit</Form.Label>
                      <Form.Control
                        name="reference"
                        type="text"
                        value={formData.reference}
                        onChange={(e) => setFormData({ ...formData, reference: e.target.value })}
                        placeholder="Référence unique"
                      />
                    </Form.Group>
                  </Col>

                  {/* Description */}
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label className="fw-medium">Description</Form.Label>
                      <Form.Control
                        name="description_produit"
                        as="textarea"
                        rows={3}
                        value={formData.description_produit}
                        onChange={(e) => setFormData({ ...formData, description_produit: e.target.value })}
                        placeholder="Description du produit"
                      />
                    </Form.Group>
                  </Col>

                  {/* Price */}
                  <Col md={4}>
                    <Form.Group>
                      <Form.Label className="fw-medium">
                        Prix (DT) <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Control
                        name="prix_produit"
                        type="number"
                        step="0.01"
                        min="0"
                        value={formData.prix_produit}
                        onChange={(e) => setFormData({ ...formData, prix_produit: e.target.value })}
                        required
                        placeholder="0.00"
                      />
                    </Form.Group>
                  </Col>

                  {/* Quantity */}
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="fw-medium">
                        Quantité <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Control
                        name="quantite_produit"
                        type="number"
                        min="0"
                        value={formData.quantite_produit}
                        onChange={(e) => setFormData({ ...formData, quantite_produit: e.target.value })}
                        required
                        placeholder="0"
                      />
                    </Form.Group>
                  </Col>

                  {/* Brand */}
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="fw-medium">
                        Marque <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Select
                        name="marque_id"
                        value={formData.marque_id}
                        onChange={(e) => setFormData({ ...formData, marque_id: e.target.value })}
                        required
                      >
                        <option value="">Sélectionnez une marque</option>
                        {brands.map((brand) => (
                          <option key={brand.id} value={brand.id}>
                            {brand.nom_marque}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>

                  {/* Category */}
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="fw-medium">
                        Catégorie <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Select
                        name="categorie_id"
                        value={formData.categorie_id}
                        onChange={(e) => setFormData({ ...formData, categorie_id: e.target.value })}
                        required
                      >
                        <option value="">Sélectionnez une catégorie</option>
                        {categories.map((category) => (
                          <option key={category.id} value={category.id}>
                            {category.nom || category.nom_categorie}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>

                  {/* Sous-Category */}
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="fw-medium">
                        Sous-catégorie <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Select
                        name="sous_categorie_id"
                        value={formData.sous_categorie_id}
                        onChange={(e) => setFormData({ ...formData, sous_categorie_id: e.target.value })}
                        required={!!formData.categorie_id}
                        disabled={!formData.categorie_id}
                      >
                        <option value="">
                          {!formData.categorie_id ? "Sélectionnez d'abord une catégorie" : 'Sélectionnez une sous-catégorie'}
                        </option>
                        {filteredSousCategories.map((sousCategory) => (
                          <option key={sousCategory.id} value={sousCategory.id}>
                            {sousCategory.nom || sousCategory.nom_sous_categorie}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>

                  {/* Sous-Sous-Category */}
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="fw-medium">
                        Sous-sous-catégorie <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Select
                        name="sous_sous_categorie_id"
                        value={formData.sous_sous_categorie_id}
                        onChange={(e) => setFormData({ ...formData, sous_sous_categorie_id: e.target.value })}
                        required={!!formData.sous_categorie_id}
                        disabled={!formData.sous_categorie_id}
                      >
                        <option value="">
                          {!formData.sous_categorie_id ? "Sélectionnez d'abord une sous-catégorie" : 'Sélectionnez une sous-sous-catégorie'}
                        </option>
                        {filteredSousSousCategories.map((sousSousCategory) => (
                          <option key={sousSousCategory.id} value={sousSousCategory.id}>
                            {sousSousCategory.nom || sousSousCategory.nom_sous_sous_categorie}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                </Row>
              </Tab>

              {/* Images Tab */}
              <Tab eventKey="images" title="Images">
                <Row className="g-3">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label className="fw-medium">Images du produit</Form.Label>
                      <Form.Control type="file" multiple accept="image/*" onChange={handleImagesChange} className="mb-3" />
                      <Form.Text className="text-muted">
                        Sélectionnez plusieurs images. Cliquez sur une image pour la définir comme principale.
                      </Form.Text>

                      {/* Existing Images (for edit mode) */}
                      {existingImages.length > 0 && (
                        <div className="mt-3">
                          <h6 className="fw-medium mb-2">Images existantes</h6>
                          <div className="alert alert-info py-2 px-3 mb-3">
                            <small>
                              <FaEye className="me-1" />
                              <strong>Cliquez sur une image</strong> pour la définir comme image principale
                            </small>
                          </div>
                          <div className="d-flex flex-wrap gap-2 mb-3">
                            {existingImages.map((img) => (
                              <div
                                key={img.id}
                                className={`position-relative border rounded ${
                                  img.is_primary ? 'border-primary border-3 shadow' : 'border-secondary'
                                }`}
                                style={{
                                  width: '120px',
                                  height: '120px',
                                  cursor: img.is_primary ? 'default' : 'pointer',
                                  opacity: img.is_primary ? 1 : 0.8
                                }}
                                onClick={() => !img.is_primary && setExistingImageAsPrimary(img.id, currentProduct.id)}
                                title={img.is_primary ? 'Image principale actuelle' : 'Cliquer pour définir comme image principale'}
                              >
                                <img
                                  src={img.thumbnail_medium || img.direct_url}
                                  alt={img.alt_text || 'Image produit'}
                                  style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                                  className="rounded"
                                />

                                {/* Badge principal - coin supérieur gauche */}
                                {img.is_primary && (
                                  <div className="position-absolute top-0 start-0 m-1">
                                    <Badge bg="primary" className="d-flex align-items-center">
                                      <FaEye className="me-1" size={10} />
                                      Principal
                                    </Badge>
                                  </div>
                                )}

                                {/* Indicateur cliquable - coin supérieur droit */}
                                {!img.is_primary && (
                                  <div className="position-absolute top-0 end-0 m-1">
                                    <Badge
                                      bg={updatingImageId === img.id ? 'warning' : 'success'}
                                      className="rounded-circle"
                                      style={{
                                        width: '24px',
                                        height: '24px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center'
                                      }}
                                    >
                                      {updatingImageId === img.id ? (
                                        <Spinner animation="border" size="sm" style={{ width: '10px', height: '10px' }} />
                                      ) : (
                                        <FaPlus size={10} />
                                      )}
                                    </Badge>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                          <hr />
                          <h6 className="fw-medium mb-2">Nouvelles images</h6>
                        </div>
                      )}

                      {images.length > 0 && (
                        <div className="d-flex mt-3 flex-wrap gap-2">
                          {images.map((img, idx) => (
                            <div
                              key={idx}
                              className={`position-relative border rounded ${
                                idx === primaryImageIndex ? 'border-primary border-3' : 'border-secondary'
                              }`}
                              style={{ cursor: 'pointer', width: '120px', height: '120px' }}
                            >
                              <img
                                src={URL.createObjectURL(img)}
                                alt={`Image ${idx + 1}`}
                                style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                                className="rounded"
                                onClick={() => setPrimaryImage(idx)}
                              />
                              {idx === primaryImageIndex && (
                                <Badge bg="primary" className="position-absolute top-0 start-0 m-1">
                                  Principale
                                </Badge>
                              )}
                              <Button
                                variant="danger"
                                size="sm"
                                className="position-absolute top-0 end-0 m-1 rounded-circle"
                                style={{ width: '24px', height: '24px', padding: '0' }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  removeImage(idx);
                                }}
                              >
                                ×
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}

                      {images.length === 0 && (
                        <div className="text-center py-4 border rounded bg-light">
                          <FaBox size={32} className="text-muted mb-2" />
                          <p className="text-muted mb-0">Aucune image sélectionnée</p>
                        </div>
                      )}
                    </Form.Group>
                  </Col>
                </Row>
              </Tab>

              {/* Attributes Tab */}
              <Tab eventKey="attributes" title="Attributs">
                <Row className="g-3">
                  {availableAttributes.length > 0 ? (
                    availableAttributes.map((attribute) => (
                      <Col md={6} key={attribute.id}>
                        <Form.Group>
                          <Form.Label className="fw-medium">
                            {attribute.nom}
                            {attribute.obligatoire && <span className="text-danger"> *</span>}
                            {attribute.groupe && <small className="text-muted ms-2">({attribute.groupe.nom})</small>}
                          </Form.Label>
                          {renderAttributeInput(attribute)}
                        </Form.Group>
                      </Col>
                    ))
                  ) : (
                    <Col md={12}>
                      <div className="text-center py-4">
                        <FaTags size={32} className="text-muted mb-2" />
                        <p className="text-muted mb-0">
                          {formData.sous_sous_categorie_id
                            ? 'Aucun attribut disponible pour cette catégorie'
                            : 'Sélectionnez une sous-sous-catégorie pour voir les attributs disponibles'}
                        </p>
                      </div>
                    </Col>
                  )}
                </Row>
              </Tab>

              {/* Variants Tab */}
              <Tab eventKey="variants" title="Variantes">
                <Row className="g-3">
                  <Col md={12}>
                    <div className="d-flex justify-content-between align-items-center mb-3">
                      <h6 className="mb-0">Variantes du produit</h6>
                      <Button variant="outline-primary" size="sm" onClick={() => setShowVariantModal(true)}>
                        <FaPlus className="me-1" />
                        Ajouter une variante
                      </Button>
                    </div>

                    {variants.length > 0 ? (
                      <Table striped bordered hover size="sm">
                        <thead>
                          <tr>
                            <th>SKU</th>
                            <th>Prix supplément</th>
                            <th>Stock</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {variants.map((variant, idx) => (
                            <tr key={variant.id}>
                              <td>{variant.sku}</td>
                              <td>{variant.prix_supplement} DT</td>
                              <td>{variant.stock}</td>
                              <td>
                                <Button variant="outline-danger" size="sm" onClick={() => removeVariant(idx)}>
                                  <FaTrashAlt />
                                </Button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </Table>
                    ) : (
                      <div className="text-center py-4 border rounded bg-light">
                        <FaLayerGroup size={32} className="text-muted mb-2" />
                        <p className="text-muted mb-0">Aucune variante créée</p>
                        <small className="text-muted">Les variantes permettent de créer différentes versions du même produit</small>
                      </div>
                    )}
                  </Col>
                </Row>
              </Tab>
            </Tabs>
          </Modal.Body>
          <Modal.Footer className="border-0 pt-0">
            <Button variant="outline-secondary" onClick={() => setShowModal(false)} disabled={submitting}>
              Annuler
            </Button>
            <Button type="submit" variant="primary" disabled={submitting}>
              {submitting ? (
                <>
                  <Spinner animation="border" size="sm" className="me-2" />
                  {modalAction === 'create' ? 'Création en cours...' : 'Modification en cours...'}
                </>
              ) : (
                <>
                  {modalAction === 'create' ? (
                    <>
                      <FaPlus className="me-2" />
                      Créer le produit
                    </>
                  ) : (
                    <>
                      <FaPencilAlt className="me-2" />
                      Modifier le produit
                    </>
                  )}
                </>
              )}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)} className="professional-modal">
        <Modal.Header closeButton className="border-0 pb-0">
          <Modal.Title className="fw-bold text-danger">
            <FaTrashAlt className="me-2" />
            Confirmer la suppression
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="px-4">
          <div className="text-center py-3">
            <FaTrashAlt size={48} className="text-danger mb-3" />
            <h5>Êtes-vous sûr de vouloir supprimer ce produit ?</h5>
            {productToDelete && (
              <div className="mt-3">
                <p className="mb-1">
                  <strong>Produit :</strong> {productToDelete.nom_produit}
                </p>
                <p className="mb-1">
                  <strong>Référence :</strong> {productToDelete.reference || 'N/A'}
                </p>
                <p className="text-muted mb-0">Cette action est irréversible.</p>
              </div>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer className="border-0 pt-0">
          <Button variant="outline-secondary" onClick={() => setShowDeleteModal(false)} disabled={loading}>
            Annuler
          </Button>
          <Button variant="danger" onClick={confirmDelete} disabled={loading}>
            {loading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Suppression...
              </>
            ) : (
              <>
                <FaTrashAlt className="me-2" />
                Supprimer
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Variant Modal */}
      <Modal show={showVariantModal} onHide={() => setShowVariantModal(false)} className="professional-modal">
        <Modal.Header closeButton className="border-0 pb-0">
          <Modal.Title className="fw-bold">
            <FaPlus className="me-2 text-primary" />
            Ajouter une Variante
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="px-4">
          <Row className="g-3">
            <Col md={6}>
              <Form.Group>
                <Form.Label className="fw-medium">
                  SKU <span className="text-danger">*</span>
                </Form.Label>
                <Form.Control
                  name="variant_sku"
                  type="text"
                  value={newVariant.sku}
                  onChange={(e) => setNewVariant({ ...newVariant, sku: e.target.value })}
                  placeholder="Ex: PROD-001-RED-L"
                  required
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group>
                <Form.Label className="fw-medium">Prix supplément (DT)</Form.Label>
                <Form.Control
                  name="variant_prix_supplement"
                  type="number"
                  step="0.01"
                  value={newVariant.prix_supplement}
                  onChange={(e) => setNewVariant({ ...newVariant, prix_supplement: parseFloat(e.target.value) || 0 })}
                  placeholder="0.00"
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group>
                <Form.Label className="fw-medium">Stock</Form.Label>
                <Form.Control
                  name="variant_stock"
                  type="number"
                  min="0"
                  value={newVariant.stock}
                  onChange={(e) => setNewVariant({ ...newVariant, stock: parseInt(e.target.value) || 0 })}
                  placeholder="0"
                />
              </Form.Group>
            </Col>

            {/* Variant Attributes */}
            {availableAttributes.length > 0 && (
              <Col md={12}>
                <hr />
                <h6>Attributs de la variante</h6>
                <Row className="g-2">
                  {availableAttributes.map((attribute) => (
                    <Col md={6} key={attribute.id}>
                      <Form.Group>
                        <Form.Label className="fw-medium">{attribute.nom}</Form.Label>
                        <Form.Control
                          name={`variant_attribute_${attribute.id}`}
                          type={attribute.type_valeur === 'nombre' ? 'number' : 'text'}
                          value={newVariant.attributs[attribute.id] || ''}
                          onChange={(e) =>
                            setNewVariant({
                              ...newVariant,
                              attributs: { ...newVariant.attributs, [attribute.id]: e.target.value }
                            })
                          }
                          placeholder={`${attribute.nom} de cette variante`}
                        />
                      </Form.Group>
                    </Col>
                  ))}
                </Row>
              </Col>
            )}
          </Row>
        </Modal.Body>
        <Modal.Footer className="border-0 pt-0">
          <Button variant="outline-secondary" onClick={() => setShowVariantModal(false)}>
            Annuler
          </Button>
          <Button variant="primary" onClick={addVariant}>
            <FaPlus className="me-2" />
            Ajouter la variante
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Product Details Modal */}
      <Modal show={showDetailsModal} onHide={() => setShowDetailsModal(false)} size="xl" className="professional-modal">
        <Modal.Header closeButton className="border-0 pb-0">
          <Modal.Title className="fw-bold">
            <FaEye className="me-2 text-info" />
            Détails du Produit
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="px-4">
          {productToView && (
            <Row className="g-4">
              {/* Product Information */}
              <Col md={6}>
                <Card className="border-0 bg-light h-100">
                  <Card.Body>
                    <h5 className="fw-bold mb-3">
                      <FaBox className="me-2 text-primary" />
                      Informations du produit
                    </h5>

                    <div className="mb-3">
                      <strong>Nom:</strong>
                      <div className="mt-1">{productToView.nom_produit}</div>
                    </div>

                    {productToView.description_produit && (
                      <div className="mb-3">
                        <strong>Description:</strong>
                        <div className="mt-1 text-muted">{productToView.description_produit}</div>
                      </div>
                    )}

                    <Row className="g-3">
                      <Col sm={6}>
                        <div className="mb-3">
                          <strong>Référence:</strong>
                          <div className="mt-1">
                            <code className="bg-white px-2 py-1 rounded border">{productToView.reference || 'N/A'}</code>
                          </div>
                        </div>
                      </Col>
                      <Col sm={6}>
                        <div className="mb-3">
                          <strong>Prix:</strong>
                          <div className="mt-1 fw-medium text-success">
                            {productToView.prix_produit ? `${productToView.prix_produit}€` : 'N/A'}
                          </div>
                        </div>
                      </Col>
                      <Col sm={6}>
                        <div className="mb-3">
                          <strong>Stock:</strong>
                          <div className="mt-1">
                            <Badge bg={productToView.quantite_produit > 0 ? 'success' : 'danger'} className="rounded-pill">
                              {productToView.quantite_produit > 0 ? `${productToView.quantite_produit} en stock` : 'Rupture'}
                            </Badge>
                          </div>
                        </div>
                      </Col>
                      <Col sm={6}>
                        <div className="mb-3">
                          <strong>Marque:</strong>
                          <div className="mt-1">{getBrandName(productToView.marque_id)}</div>
                        </div>
                      </Col>
                    </Row>

                    <div className="mb-3">
                      <strong>Catégorie:</strong>
                      <div className="mt-1">
                        <small className="text-muted">{getCategoryPath(productToView.sous_sous_categorie_id)}</small>
                      </div>
                    </div>
                  </Card.Body>
                </Card>
              </Col>

              {/* Product Images Gallery */}
              <Col md={6}>
                <Card className="border-0 bg-light h-100">
                  <Card.Body>
                    <h5 className="fw-bold mb-3">
                      <FaBox className="me-2 text-primary" />
                      Galerie d'images ({(productImages[productToView.id] || []).length})
                    </h5>

                    {(productImages[productToView.id] || []).length > 0 ? (
                      <>
                        <div className="alert alert-info py-2 px-3 mb-3">
                          <small>
                            <FaEye className="me-1" />
                            <strong>Cliquez sur une image</strong> pour la définir comme principale •<strong> Bouton œil</strong> pour voir
                            en grand
                          </small>
                        </div>
                        <div className="row g-2">
                          {(productImages[productToView.id] || []).map((image, index) => (
                            <div key={image.id} className="col-6 col-lg-4">
                              <div className="position-relative">
                                <img
                                  src={image.thumbnail_medium || image.direct_url}
                                  alt={image.alt_text || `Image ${index + 1}`}
                                  className={`w-100 rounded border ${
                                    image.is_primary ? 'border-primary border-3 shadow' : 'border-secondary'
                                  }`}
                                  style={{
                                    height: '120px',
                                    objectFit: 'cover',
                                    cursor: image.is_primary ? 'default' : 'pointer',
                                    opacity: image.is_primary ? 1 : 0.8
                                  }}
                                  onClick={() => !image.is_primary && setExistingImageAsPrimary(image.id, productToView.id)}
                                  title={image.is_primary ? 'Image principale actuelle' : 'Cliquer pour définir comme image principale'}
                                />

                                {/* Badge principal - coin supérieur gauche */}
                                {image.is_primary && (
                                  <div className="position-absolute top-0 start-0 m-1">
                                    <Badge bg="primary" className="d-flex align-items-center">
                                      <FaEye className="me-1" size={10} />
                                      Principal
                                    </Badge>
                                  </div>
                                )}

                                {/* Indicateur cliquable - coin inférieur gauche */}
                                {!image.is_primary && (
                                  <div className="position-absolute bottom-0 start-0 m-1">
                                    <Badge
                                      bg={updatingImageId === image.id ? 'warning' : 'success'}
                                      className="rounded-circle"
                                      style={{
                                        width: '24px',
                                        height: '24px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center'
                                      }}
                                    >
                                      {updatingImageId === image.id ? (
                                        <Spinner animation="border" size="sm" style={{ width: '10px', height: '10px' }} />
                                      ) : (
                                        <FaPlus size={10} />
                                      )}
                                    </Badge>
                                  </div>
                                )}

                                {/* Bouton voir en grand - coin supérieur droit */}
                                <div className="position-absolute top-0 end-0 m-1">
                                  <Button
                                    variant="light"
                                    size="sm"
                                    className="rounded-circle p-1"
                                    style={{ width: '28px', height: '28px' }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      window.open(image.direct_url, '_blank');
                                    }}
                                    title="Voir en grand"
                                  >
                                    <FaEye size={12} />
                                  </Button>
                                </div>
                              </div>
                              {image.alt_text && <small className="text-muted d-block mt-1 text-truncate">{image.alt_text}</small>}
                            </div>
                          ))}
                        </div>
                      </>
                    ) : (
                      <div className="text-center py-5">
                        <FaBox size={48} className="text-muted mb-3" />
                        <h6 className="text-muted">Aucune image</h6>
                        <p className="text-muted mb-0">Ce produit n'a pas d'images associées</p>
                      </div>
                    )}
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          )}
        </Modal.Body>
        <Modal.Footer className="border-0 pt-0">
          <Button variant="outline-secondary" onClick={() => setShowDetailsModal(false)}>
            Fermer
          </Button>
          {productToView && (
            <Button
              variant="primary"
              onClick={() => {
                setShowDetailsModal(false);
                handleEdit(productToView);
              }}
            >
              <FaPencilAlt className="me-2" />
              Modifier ce produit
            </Button>
          )}
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default ProductManagement;
